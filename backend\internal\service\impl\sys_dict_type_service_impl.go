package impl

import (
	"fmt"

	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/redis"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/pkg/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	// DictKeyPrefix 字典键名前缀
	DictKeyPrefix = "sys_dict:"
)

// SysDictTypeServiceImpl 字典类型服务实现
type SysDictTypeServiceImpl struct {
	logger       *zap.Logger
	dictTypeRepo repository.DictTypeRepository
	dictDataRepo repository.DictDataRepository
	redisService redis.RedisService
}

// NewSysDictTypeServiceImpl 创建字典类型服务实现
func NewSysDictTypeServiceImpl(logger *zap.Logger, dictTypeRepo repository.DictTypeRepository, dictDataRepo repository.DictDataRepository, redisService redis.RedisService) service.ISysDictTypeService {
	return &SysDictTypeServiceImpl{
		logger:       logger,
		dictTypeRepo: dictTypeRepo,
		dictDataRepo: dictDataRepo,
		redisService: redisService,
	}
}

// Init 初始化方法，加载字典缓存数据
func (s *SysDictTypeServiceImpl) Init() {
	s.LoadingDictCache()
}

// SelectDictTypeList 根据条件分页查询字典类型
func (s *SysDictTypeServiceImpl) SelectDictTypeList(dictType domain.SysDictType) []domain.SysDictType {
	dictTypePtr := &domain.SysDictType{
		DictName: dictType.DictName,
		DictType: dictType.DictType,
		Status:   dictType.Status,
	}
	dictTypePtr.SetParams(dictType.GetParams())

	dictTypeList, err := s.dictTypeRepo.SelectDictTypeList(dictTypePtr)
	if err != nil {
		s.logger.Error("查询字典类型列表失败", zap.Error(err))
		return []domain.SysDictType{}
	}
	return dictTypeList
}

// SelectDictTypeAll 查询所有字典类型
func (s *SysDictTypeServiceImpl) SelectDictTypeAll() []domain.SysDictType {
	dictTypeList, err := s.dictTypeRepo.SelectDictTypeAll()
	if err != nil {
		s.logger.Error("查询所有字典类型失败", zap.Error(err))
		return []domain.SysDictType{}
	}
	return dictTypeList
}

// SelectDictDataByType 根据字典类型查询字典数据
func (s *SysDictTypeServiceImpl) SelectDictDataByType(dictType string) []domain.SysDictData {
	// 先从缓存中获取
	dictDataListStr, err := s.redisService.Get(s.GetDictCacheKey(dictType))
	if err == nil && dictDataListStr != "" {
		// 从缓存中反序列化字典数据列表
		var dictDataList []domain.SysDictData
		err = utils.FromJSON(dictDataListStr, &dictDataList)
		if err == nil && len(dictDataList) > 0 {
			s.logger.Debug("从缓存中获取字典数据", zap.String("dictType", dictType), zap.Int("count", len(dictDataList)))
			return dictDataList
		}
	}

	// 缓存中没有，从数据库获取
	dictDataList, err := s.dictDataRepo.SelectDictDataByType(dictType)
	if err != nil {
		s.logger.Error("根据字典类型查询字典数据失败", zap.String("dictType", dictType), zap.Error(err))
		return []domain.SysDictData{}
	}

	// 放入缓存
	if len(dictDataList) > 0 {
		jsonStr, err := utils.ToJSON(dictDataList)
		if err == nil {
			// 设置缓存，不设置过期时间（由缓存清理策略管理）
			err = s.redisService.Set(s.GetDictCacheKey(dictType), jsonStr, 0)
			if err != nil {
				s.logger.Error("缓存字典数据失败", zap.String("dictType", dictType), zap.Error(err))
			} else {
				s.logger.Debug("缓存字典数据成功", zap.String("dictType", dictType), zap.Int("count", len(dictDataList)))
			}
		} else {
			s.logger.Error("序列化字典数据失败", zap.String("dictType", dictType), zap.Error(err))
		}
	}

	return dictDataList
}

// SelectDictTypeById 根据字典类型ID查询信息
func (s *SysDictTypeServiceImpl) SelectDictTypeById(dictId int64) domain.SysDictType {
	dictType, err := s.dictTypeRepo.SelectDictTypeById(dictId)
	if err != nil {
		s.logger.Error("根据字典类型ID查询信息失败", zap.Int64("dictId", dictId), zap.Error(err))
		return domain.SysDictType{}
	}
	if dictType == nil {
		return domain.SysDictType{}
	}
	return *dictType
}

// SelectDictTypeByType 根据字典类型查询信息
func (s *SysDictTypeServiceImpl) SelectDictTypeByType(dictType string) domain.SysDictType {
	result, err := s.dictTypeRepo.SelectDictTypeByType(dictType)
	if err != nil {
		s.logger.Error("根据字典类型查询信息失败", zap.String("dictType", dictType), zap.Error(err))
		return domain.SysDictType{}
	}
	if result == nil {
		return domain.SysDictType{}
	}
	return *result
}

// DeleteDictTypeByIds 批量删除字典信息
func (s *SysDictTypeServiceImpl) DeleteDictTypeByIds(dictIds []int64) {
	// 查询所有要删除的字典类型
	var dictTypes []domain.SysDictType
	for _, dictId := range dictIds {
		dictType, err := s.dictTypeRepo.SelectDictTypeById(dictId)
		if err == nil && dictType != nil {
			dictTypes = append(dictTypes, *dictType)
		}
	}

	// 检查是否有字典类型正在使用
	for _, dictType := range dictTypes {
		count, err := s.dictDataRepo.CountDictDataByType(dictType.DictType)
		if err != nil {
			s.logger.Error("检查字典类型是否使用失败", zap.String("dictType", dictType.DictType), zap.Error(err))
			continue
		}
		if count > 0 {
			s.logger.Warn("字典类型已分配，不能删除", zap.String("dictType", dictType.DictType))
			continue
		}

		// 删除字典类型
		err = s.dictTypeRepo.DeleteDictTypeById(dictType.DictId)
		if err != nil {
			s.logger.Error("删除字典类型失败", zap.Int64("dictId", dictType.DictId), zap.Error(err))
			continue
		}

		// 删除缓存
		s.redisService.Del(s.GetDictCacheKey(dictType.DictType))
	}
}

// LoadingDictCache 加载字典缓存数据
func (s *SysDictTypeServiceImpl) LoadingDictCache() {
	dictTypeList, err := s.dictTypeRepo.SelectDictTypeAll()
	if err != nil {
		s.logger.Error("加载字典缓存数据失败", zap.Error(err))
		return
	}

	for _, dictType := range dictTypeList {
		dictDataList, err := s.dictDataRepo.SelectDictDataByType(dictType.DictType)
		if err != nil {
			s.logger.Error("加载字典数据失败", zap.String("dictType", dictType.DictType), zap.Error(err))
			continue
		}

		// 将字典数据列表序列化为JSON字符串并存入缓存
		if len(dictDataList) > 0 {
			jsonStr, err := utils.ToJSON(dictDataList)
			if err == nil {
				// 设置缓存，不设置过期时间（由缓存清理策略管理）
				err = s.redisService.Set(s.GetDictCacheKey(dictType.DictType), jsonStr, 0)
				if err != nil {
					s.logger.Error("缓存字典数据失败", zap.String("dictType", dictType.DictType), zap.Error(err))
				} else {
					s.logger.Debug("缓存字典数据成功", zap.String("dictType", dictType.DictType), zap.Int("count", len(dictDataList)))
				}
			} else {
				s.logger.Error("序列化字典数据失败", zap.String("dictType", dictType.DictType), zap.Error(err))
			}
		}
	}
}

// ClearDictCache 清空字典缓存数据
func (s *SysDictTypeServiceImpl) ClearDictCache() {
	dictTypeList, err := s.dictTypeRepo.SelectDictTypeAll()
	if err != nil {
		s.logger.Error("清空字典缓存数据失败", zap.Error(err))
		return
	}

	for _, dictType := range dictTypeList {
		s.redisService.Del(s.GetDictCacheKey(dictType.DictType))
	}
}

// ResetDictCache 重置字典缓存数据
func (s *SysDictTypeServiceImpl) ResetDictCache() {
	s.ClearDictCache()
	s.LoadingDictCache()
}

// InsertDictType 新增保存字典类型信息
func (s *SysDictTypeServiceImpl) InsertDictType(dictType domain.SysDictType) int {
	dictTypePtr := &domain.SysDictType{
		DictName: dictType.DictName,
		DictType: dictType.DictType,
		Status:   dictType.Status,
	}

	// 设置BaseEntity字段
	dictTypePtr.CreateBy = dictType.CreateBy
	dictTypePtr.CreateTime = dictType.CreateTime
	dictTypePtr.UpdateBy = dictType.UpdateBy
	dictTypePtr.UpdateTime = dictType.UpdateTime
	dictTypePtr.Remark = dictType.Remark

	err := s.dictTypeRepo.InsertDictType(dictTypePtr)
	if err != nil {
		s.logger.Error("新增保存字典类型信息失败", zap.Error(err))
		return 0
	}
	return 1
}

// UpdateDictType 修改保存字典类型信息
func (s *SysDictTypeServiceImpl) UpdateDictType(dictType domain.SysDictType) int {
	// 查询原字典类型
	oldDict, err := s.dictTypeRepo.SelectDictTypeById(dictType.DictId)
	if err != nil {
		s.logger.Error("查询原字典类型失败", zap.Int64("dictId", dictType.DictId), zap.Error(err))
		return 0
	}

	dictTypePtr := &domain.SysDictType{
		DictId:   dictType.DictId,
		DictName: dictType.DictName,
		DictType: dictType.DictType,
		Status:   dictType.Status,
	}

	// 设置BaseEntity字段
	dictTypePtr.UpdateBy = dictType.UpdateBy
	dictTypePtr.UpdateTime = dictType.UpdateTime
	dictTypePtr.Remark = dictType.Remark

	// 使用事务更新字典类型和字典数据
	err = s.dictTypeRepo.GetDB().Transaction(func(tx *gorm.DB) error {
		// 更新字典类型
		dictTypeRepoTx := s.dictTypeRepo.WithTx(tx).(repository.DictTypeRepository)
		err := dictTypeRepoTx.UpdateDictType(dictTypePtr)
		if err != nil {
			return err
		}

		// 如果字典类型发生变化，则更新字典数据的字典类型
		if oldDict != nil && oldDict.DictType != dictType.DictType {
			dictDataRepoTx := s.dictDataRepo.WithTx(tx).(repository.DictDataRepository)
			err = dictDataRepoTx.UpdateDictDataType(oldDict.DictType, dictType.DictType)
			if err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		s.logger.Error("修改保存字典类型信息失败", zap.Error(err))
		return 0
	}

	// 清除旧的缓存
	if oldDict != nil {
		s.redisService.Del(s.GetDictCacheKey(oldDict.DictType))
	}
	// 清除新的缓存
	s.redisService.Del(s.GetDictCacheKey(dictType.DictType))

	return 1
}

// CheckDictTypeUnique 校验字典类型称是否唯一
func (s *SysDictTypeServiceImpl) CheckDictTypeUnique(dictType domain.SysDictType) bool {
	existDictType, err := s.dictTypeRepo.CheckDictTypeUnique(dictType.DictType)
	if err != nil {
		s.logger.Error("校验字典类型称是否唯一失败", zap.Error(err))
		return false
	}

	if existDictType != nil && existDictType.DictId != dictType.DictId {
		return false
	}
	return true
}

// GetDictCacheKey 获取字典缓存键值
func (s *SysDictTypeServiceImpl) GetDictCacheKey(dictType string) string {
	return fmt.Sprintf("%s%s", DictKeyPrefix, dictType)
}
